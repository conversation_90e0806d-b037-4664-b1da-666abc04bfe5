# 电机PID控制系统速度优化 PRD

## 文档信息
- **版本**: v1.0
- **创建日期**: 2025-01-03
- **负责人**: Emma (产品经理)
- **项目**: 激光跟踪系统电机控制优化

## 背景与问题陈述

### 当前问题
当前激光跟踪系统中，电机PID控制虽然非常稳定，但转动速度过慢，影响系统的响应性能。

### 技术背景
1. **系统架构**: 视觉识别(MaixCam) -> PID计算(mypid.c) -> 步进电机控制(step_motor_bsp.c) -> EMM V5协议
2. **PID参数**: pid_x和pid_y设置为Kp=3, Ki=1, Kd=0.02
3. **输出限制**: PID最大输出限制为max_out=3 RPM
4. **电机规格**: 最大速度MOTOR_MAX_SPEED=1000 RPM
5. **控制目标**: bp激光坐标跟踪red激光坐标

### 核心问题
PID输出被严重限制在3 RPM以内，相对于1000 RPM的电机最大速度过于保守，导致激光跟踪响应速度缓慢。

## 目标与成功指标

### 项目目标 (Objectives)
1. **主要目标**: 在保持PID控制稳定性的前提下，显著提升电机转动速度
2. **次要目标**: 提升激光跟踪系统的响应性能和用户体验

### 关键结果 (Key Results)
1. **速度提升**: 电机实际运行速度从3 RPM提升到45 RPM（15倍提升）
2. **稳定性保持**: PID控制参数保持不变，系统稳定性不受影响
3. **精度维持**: 激光跟踪精度保持在原有水平
4. **安全运行**: 系统运行安全，无异常振荡或超调现象

### 反向指标 (Counter Metrics)
1. **系统稳定性**: 不允许出现振荡、超调或失控现象
2. **跟踪精度**: 激光跟踪误差不得超过原有水平
3. **系统安全**: 电机速度不得超过安全阈值

## 用户画像与用户故事

### 目标用户
- **主要用户**: 激光跟踪系统操作员
- **次要用户**: 系统维护工程师

### 用户故事
1. **作为操作员**，我希望激光跟踪系统响应更快，这样我可以更高效地完成跟踪任务
2. **作为维护工程师**，我希望系统保持稳定可靠，避免因速度优化导致的系统问题

## 功能规格详述

### 核心功能
1. **速度比例映射**: 在PID输出和电机速度之间添加可配置的比例系数
2. **参数配置**: 支持X轴和Y轴独立的速度比例系数配置
3. **安全保护**: 保持原有PID输出限制作为底层安全机制### 技术实现方案
```c
// 在pi_bsp.h中添加速度比例系数宏定义
#define MOTOR_SPEED_SCALE_X    15.0f    // X轴速度比例系数
#define MOTOR_SPEED_SCALE_Y    15.0f    // Y轴速度比例系数

// 在pi_bsp.c的pi_proc函数中应用比例系数
void pi_proc(void)
{
    float pos_out_x, pos_out_y = 0;
    
    pos_out_x = pid_calc(&pid_x, latest_bp_laser_coord.x, latest_red_laser_coord.x, 0);
    pos_out_y = pid_calc(&pid_y, latest_bp_laser_coord.y, latest_red_laser_coord.y, 0);
    
    // 应用速度比例系数映射
    Step_Motor_Set_Speed_my(-pos_out_x * MOTOR_SPEED_SCALE_X, pos_out_y * MOTOR_SPEED_SCALE_Y);
}
```

### 业务逻辑规则
1. **比例系数范围**: 建议范围10-25倍，初始设置15倍
2. **方向控制**: 保持原有X轴负号逻辑不变
3. **参数调整**: 支持运行时通过宏定义调整比例系数

### 边缘情况与异常处理
1. **比例系数过大**: 如果导致系统不稳定，逐步降低数值
2. **比例系数过小**: 如果响应仍然偏慢，适当增加数值
3. **系统异常**: 保持原有PID输出限制作为安全保障

## 范围定义

### 包含功能 (In Scope)
1. 添加速度比例系数宏定义
2. 修改PID输出速度映射逻辑
3. 系统集成测试与参数调优
4. 性能验证和安全测试

### 排除功能 (Out of Scope)
1. 修改PID控制器参数（Kp、Ki、Kd）
2. 更改PID输出限制（max_out=3）
3. 修改电机硬件配置
4. 更改视觉识别算法## 依赖与风险

### 内部依赖
1. **代码依赖**: pi_bsp.h和pi_bsp.c文件
2. **编译环境**: Keil MDK-ARM开发环境
3. **硬件依赖**: EMM V5步进电机和控制器

### 外部依赖
1. **测试环境**: 完整的激光跟踪测试平台
2. **测试数据**: 标准的激光跟踪测试场景

### 潜在风险
1. **技术风险**: 比例系数设置不当可能导致系统不稳定
2. **性能风险**: 速度提升可能影响跟踪精度
3. **安全风险**: 电机速度过快可能导致机械损坏

### 风险缓解策略
1. **渐进式调整**: 从保守的比例系数开始，逐步优化
2. **充分测试**: 在各种场景下进行全面测试
3. **回滚机制**: 保留原始配置作为备份方案

## 发布初步计划

### 开发阶段
1. **阶段1**: 添加速度比例系数宏定义（1天）
2. **阶段2**: 修改PID输出速度映射逻辑（1天）
3. **阶段3**: 系统集成测试与参数调优（2-3天）

### 测试策略
1. **单元测试**: 验证代码修改的正确性
2. **集成测试**: 验证系统整体功能
3. **性能测试**: 验证速度提升效果
4. **稳定性测试**: 长时间运行验证系统稳定性

### 上线计划
1. **灰度测试**: 在受控环境下进行初步验证
2. **全量部署**: 确认无问题后进行正式部署
3. **监控跟踪**: 部署后持续监控系统性能

## 验收标准

### 功能验收
1. 编译通过，无语法错误
2. 电机响应速度明显提升
3. 激光跟踪功能正常工作
4. 系统运行稳定，无异常现象

### 性能验收
1. 电机实际速度达到45 RPM左右
2. 激光跟踪响应时间显著缩短
3. 跟踪精度保持在原有水平
4. 系统稳定性不受影响

### 安全验收
1. 无系统振荡或超调现象
2. 电机运行在安全速度范围内
3. 异常情况下系统能够安全停止
4. 保持原有安全保护机制有效